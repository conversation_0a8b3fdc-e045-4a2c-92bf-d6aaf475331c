# To run this code you need to install the following dependencies:
# pip install google-genai

import os
from google import genai
from google.genai import types
from typing import Union
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
import hashlib
from datetime import datetime, timezone, timedelta
loggers = setup_new_logging(__name__)

async  def generate_image(current_user: UserTenantDB, keyword: Union[str, list[str]   ]):
    if isinstance(keyword,list):
        keyword = ", ".join(keyword)
    loggers.info(f"Generating imagen for keyword: {keyword}")

    # Check if image already exists in media cache
    cached_media = await _check_media_cache(current_user, keyword, "image", "imagen_prompt")
    if cached_media:
        loggers.info(f"🎯 Using cached image for keyword: {keyword}")
        return cached_media["file_text"], cached_media["file_info"], cached_media["usage_metadata"]

    prompt_data=await current_user.async_db.prompts.find_one({"name": "imagen_prompt"})
    loggers.info(f"Prompt data: {prompt_data}")
    prompt = prompt_data.get("prompt", "").format(keyword=keyword)
    client = genai.Client(
        api_key= os.environ.get("GEMINI_API_KEY"),
    )

    model = "gemini-2.0-flash-preview-image-generation"
    contents = [
        types.Content(
            role="user",
            parts=[
                types.Part.from_text(text=prompt),
            ],
        ),
    ]
    generate_content_config = types.GenerateContentConfig(
        response_modalities=[
            "IMAGE",
            "TEXT",
        ],
        response_mime_type="text/plain",
    )
    loggers.debug(f"Generating image for: {keyword}")

    file_text = ""
    file_bytes = b""
    usage_metadata = {}

    try:
        for chunk in client.models.generate_content_stream(
            model=model,
            contents=contents,
            config=generate_content_config,
        ):
            if (
                chunk.candidates is None
                or chunk.candidates[0].content is None
                or chunk.candidates[0].content.parts is None
            ):
                continue

            # Process each part in the chunk
            for part in chunk.candidates[0].content.parts:
                if part.text:
                    file_text += part.text
                if part.inline_data and part.inline_data.data:
                    file_bytes += part.inline_data.data

            # Capture usage metadata from chunks
            if chunk.usage_metadata:
                usage_metadata = chunk.usage_metadata

    except Exception as api_error:
        loggers.error(f"Image generation failed for '{keyword}': {api_error}")
        return None, None, {}

    # Check if we got image data
    if not file_bytes:
        loggers.error(f"No image data received for: {keyword}")
        return None, None, usage_metadata

    meta = usage_metadata

    # Save to MinIO
    from app.shared.async_minio_client import create_async_minio_client

    async_minio_client = create_async_minio_client(current_user.minio)
    file_info = await async_minio_client.save_file_async(
        data=file_bytes,
        user_id=current_user.user.id,
        content_type="image/jpeg",
        folder="imagen",
        file_extension=".jpg",
        # custom_filename=hashlib.sha256(f"{keyword}_image.jpg").hexdigest()[:10]+".jpg"
        custom_filename = hashlib.sha256(f"{keyword}_image.jpg".encode()).hexdigest()[:10] + ".jpg"

    )

    loggers.info(f"Imagen, Image generated and saved: {keyword} in {file_info}")

    # Cache the generated image for future use
    await _save_media_cache(current_user, keyword, "image", "imagen_prompt", file_text, file_info, meta)

    return file_text, file_info, meta


async def _check_media_cache(current_user: UserTenantDB, keyword: str, media_type: str, prompt_type: str):
    """
    Check if media already exists in cache for the given keyword.

    Args:
        current_user: User context with database access
        keyword: The keyword/text used to generate media
        media_type: Type of media ("audio" or "image")
        prompt_type: Type of prompt used (e.g., "audio_prompt", "imagen_prompt")

    Returns:
        Cached media data if found, None otherwise
    """
    try:
        # Create a unique cache key based on keyword, media type, and prompt type
        cache_key = hashlib.sha256(f"{keyword}_{media_type}_{prompt_type}".encode()).hexdigest()

        # Check if media exists in cache
        cached_media = await current_user.async_db.media.find_one({
            "cache_key": cache_key,
            "keyword": keyword,
            "media_type": media_type,
            "prompt_type": prompt_type
        })

        if cached_media:
            # Update user access tracking
            user_ids = cached_media.get("user_ids", [])
            if current_user.user.id not in user_ids:
                user_ids.append(current_user.user.id)
                await current_user.async_db.media.update_one(
                    {"_id": cached_media["_id"]},
                    {
                        "$set": {
                            "user_ids": user_ids,
                            "last_accessed_at": datetime.now(timezone.utc),
                            "last_accessed_by": current_user.user.id
                        }
                    }
                )
            else:
                # Just update last access time
                await current_user.async_db.media.update_one(
                    {"_id": cached_media["_id"]},
                    {
                        "$set": {
                            "last_accessed_at": datetime.now(timezone.utc),
                            "last_accessed_by": current_user.user.id
                        }
                    }
                )

            loggers.info(f"🎯 Found cached {media_type} for keyword: {keyword}")

            # Generate fresh presigned URL for the cached media
            try:
                presigned_url = current_user.minio.get_presigned_url(
                    bucket_name=current_user.minio_bucket_name,
                    object_name=cached_media["object_name"],
                    expires=timedelta(hours=24),
                    method="GET"
                )

                # Update file_info with fresh URL
                file_info = cached_media["file_info"].copy()
                file_info["url"] = presigned_url

                return {
                    "file_text": cached_media.get("file_text", ""),
                    "file_info": file_info,
                    "usage_metadata": cached_media.get("usage_metadata", {})
                }

            except Exception as url_error:
                loggers.error(f"❌ Error generating presigned URL for cached media: {url_error}")
                # If URL generation fails, we'll regenerate the media
                return None

        return None

    except Exception as e:
        loggers.error(f"❌ Error checking media cache: {e}")
        return None


async def _save_media_cache(current_user: UserTenantDB, keyword: str, media_type: str, prompt_type: str,
                           file_text: str, file_info: dict, usage_metadata: dict):
    """
    Save generated media to cache for future reuse.

    Args:
        current_user: User context with database access
        keyword: The keyword/text used to generate media
        media_type: Type of media ("audio" or "image")
        prompt_type: Type of prompt used (e.g., "audio_prompt", "imagen_prompt")
        file_text: Generated text content
        file_info: File information from MinIO
        usage_metadata: API usage metadata
    """
    try:
        # Create a unique cache key
        cache_key = hashlib.sha256(f"{keyword}_{media_type}_{prompt_type}".encode()).hexdigest()

        # Check if media already exists
        existing_media = await current_user.async_db.media.find_one({
            "cache_key": cache_key
        })

        if existing_media:
            # Add current user to user_ids list if not already present
            user_ids = existing_media.get("user_ids", [])
            if current_user.user.id not in user_ids:
                user_ids.append(current_user.user.id)

            await current_user.async_db.media.update_one(
                {"cache_key": cache_key},
                {
                    "$set": {
                        "user_ids": user_ids,
                        "last_accessed_at": datetime.now(timezone.utc),
                        "last_accessed_by": current_user.user.id
                    }
                }
            )
            loggers.info(f"💾 Updated user access for cached {media_type} for keyword: {keyword}")
        else:
            # Prepare cache document with user_ids list
            cache_document = {
                "cache_key": cache_key,
                "keyword": keyword,
                "media_type": media_type,
                "prompt_type": prompt_type,
                "file_text": file_text,
                "file_info": file_info,
                "usage_metadata": usage_metadata.model_dump() if hasattr(usage_metadata, 'model_dump') else usage_metadata,
                "object_name": file_info.get("object_name"),
                "folder": file_info.get("folder"),
                "content_type": file_info.get("content_type"),
                "file_size": file_info.get("file_size"),
                "created_at": datetime.now(timezone.utc),
                "user_ids": [current_user.user.id],  # Store as list for multi-user access
                "created_by": current_user.user.id,
                "last_accessed_at": datetime.now(timezone.utc),
                "last_accessed_by": current_user.user.id
            }

            # Save to media collection (upsert to avoid duplicates)
            await current_user.async_db.media.update_one(
                {"cache_key": cache_key},
                {"$set": cache_document},
                upsert=True
            )
            loggers.info(f"💾 Cached {media_type} for keyword: {keyword} with multi-user support")

    except Exception as e:
        loggers.error(f"❌ Error saving media cache: {e}")
        # Don't fail the main operation if caching fails

