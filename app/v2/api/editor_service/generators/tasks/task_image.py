# To run this code you need to install the following dependencies:
# pip install google-genai

import os
from google import genai
from google.genai import types
from typing import Union
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
import hashlib
loggers = setup_new_logging(__name__)

async  def generate(current_user: UserTenantDB, keyword: Union[str, list[str]   ]):
    if isinstance(keyword,list):
        keyword = ", ".join(keyword)
    loggers.info(f"Generating imagen for keyword: {keyword}")


    prompt_data=await current_user.async_db.prompts.find_one({"name": "imagen_prompt"})
    loggers.info(f"Prompt data: {prompt_data}")
    prompt = prompt_data.get("prompt", "").format(keyword=keyword)
    client = genai.Client(
        api_key= os.environ.get("GEMINI_API_KEY"),
    )

    model = "gemini-2.0-flash-preview-image-generation"
    contents = [
        types.Content(
            role="user",
            parts=[
                types.Part.from_text(text=prompt),
            ],
        ),
    ]
    generate_content_config = types.GenerateContentConfig(
        response_modalities=[
            "IMAGE",
            "TEXT",
        ],
        response_mime_type="text/plain",
    )
    loggers.debug(f"Generating image for: {keyword}")

    file_text = ""
    file_bytes = b""
    usage_metadata = {}

    try:
        for chunk in client.models.generate_content_stream(
            model=model,
            contents=contents,
            config=generate_content_config,
        ):
            if (
                chunk.candidates is None
                or chunk.candidates[0].content is None
                or chunk.candidates[0].content.parts is None
            ):
                continue

            # Process each part in the chunk
            for part in chunk.candidates[0].content.parts:
                if part.text:
                    file_text += part.text
                if part.inline_data and part.inline_data.data:
                    file_bytes += part.inline_data.data

            # Capture usage metadata from chunks
            if chunk.usage_metadata:
                usage_metadata = chunk.usage_metadata

    except Exception as api_error:
        loggers.error(f"Image generation failed for '{keyword}': {api_error}")
        return None, None, {}

    # Check if we got image data
    if not file_bytes:
        loggers.error(f"No image data received for: {keyword}")
        return None, None, usage_metadata

    meta = usage_metadata

    # Save to MinIO
    from app.shared.async_minio_client import create_async_minio_client

    async_minio_client = create_async_minio_client(current_user.minio)
    file_info = await async_minio_client.save_file_async(
        data=file_bytes,
        user_id=current_user.user.id,
        content_type="image/jpeg",
        folder="imagen",
        file_extension=".jpg",
        # custom_filename=hashlib.sha256(f"{keyword}_image.jpg").hexdigest()[:10]+".jpg"
        custom_filename = hashlib.sha256(f"{keyword}_image.jpg".encode()).hexdigest()[:10] + ".jpg"

    )

    loggers.info(f"TaskImage Image generated and saved: {keyword} in {file_info}")
    return file_text, file_info, meta

