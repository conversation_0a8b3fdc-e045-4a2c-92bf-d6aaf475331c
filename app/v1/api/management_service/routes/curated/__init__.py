from fastapi import APIRouter

from app.v1.api.management_service.routes.curated import (
    curated_contents,
    questions,
    word_list,
    theme_management
)

router = APIRouter(
    responses={404: {"description": "Not found"}}
)

router.include_router(curated_contents.router)
router.include_router(questions.router)
router.include_router(word_list.router)
router.include_router(theme_management.router)
